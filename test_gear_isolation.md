# 档位隔离问题修复验证

## 问题描述
点击返回按钮两次后，用户设置的档位被修改成了15档。

## 根本原因分析

### 1. 问题流程
1. 用户设置档位为某个值（比如5档）
2. 页面触发 `handle4F4EData()` 方法进行距离监听
3. `handle4F4EData()` 硬编码发送15档指令到设备
4. 1秒后调用 `checkjl()` 查询当前档位
5. 设备返回15档，UI被更新为15档

### 2. 核心问题代码
```typescript
// 原问题代码
async handle4F4EData() {
  let results = decimalToHexadecimal(15);  // 🚨 硬编码15档
  let data = "FFCC0100" + results + "BBAA";
  await sendData(data);
  
  setTimeout(async () => {
    if (this.data.arrlist.length > 0) {
      this.cmMinMax(this.data.arrlist);
      this.checkjl();  // 🚨 查询档位，导致UI被更新为15
    }
  }, 1000);
}
```

## 修复方案

### 1. 保存和恢复用户档位
- 在距离监听前保存用户设置的档位
- 监听完成后恢复用户档位，而不是查询设备档位

### 2. 命令来源标识
- 添加 `distance_monitoring` 命令来源标识
- 在数据监听中区分不同来源的档位查询

### 3. 修复后的代码逻辑
```typescript
async handle4F4EData() {
  // 保存用户当前设置的档位
  const userSetGear = this.data.silderValue1;
  
  // 标记为距离监听来源
  this.setData({ commandSource: "distance_monitoring" });
  
  // 临时设置为15档进行距离监听
  let results = decimalToHexadecimal(15);
  let data = "FFCC0100" + results + "BBAA";
  await sendData(data);
  
  setTimeout(async () => {
    if (this.data.arrlist.length > 0) {
      this.cmMinMax(this.data.arrlist);
      
      // 恢复用户设置的档位，而不是查询当前档位
      if (userSetGear !== 15) {
        await this.setInduction(userSetGear);
      }
    }
    this.setData({ commandSource: "idle" });
  }, 1000);
}
```

## 测试验证步骤

1. **设置用户档位**
   - 将滑块设置为5档
   - 确认 `silderValue1` 为5

2. **触发距离监听**
   - 调用 `handle4F4EData()`
   - 观察设备是否临时设置为15档

3. **验证档位恢复**
   - 监听完成后检查 `silderValue1` 是否仍为5
   - 确认设备实际档位也恢复为5

4. **验证UI不受影响**
   - 在距离监听过程中，UI显示的档位应保持为5
   - 不应该因为设备临时的15档而更新UI

## 预期效果
- 用户设置的档位在距离监听前后保持不变
- 距离监听使用15档获取最大范围数据
- 监听完成后自动恢复用户原始档位设置
- UI显示始终反映用户的真实设置，不受监听过程影响
